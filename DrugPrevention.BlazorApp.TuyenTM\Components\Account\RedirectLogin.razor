﻿@page "/Account/RedirectLogin/{username}"
@using System.Security.Claims
@using Microsoft.AspNetCore.Authentication.Cookies
@inject IHttpContextAccessor HttpContextAccessor
@using Microsoft.AspNetCore.Authentication


@code {
    [Parameter]
    public string? Username { get; set; }

    protected override async Task OnInitializedAsync()
    {
        if (string.IsNullOrWhiteSpace(Username))
        {
            navigationManager.NavigateTo("/Account/Login");
            return;
        }

        var user = await ServiceProviders.systemUserAccountService.GetUserAccountByUserNameAsync(Username);
        if (user != null)
        {
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.Name, user.UserName),
                new Claim(ClaimTypes.Role, user.RoleId.ToString())
            };

            var identity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);
            var principal = new ClaimsPrincipal(identity);

            await HttpContextAccessor.HttpContext.SignInAsync(
                CookieAuthenticationDefaults.AuthenticationScheme,
                principal,
                new AuthenticationProperties
                {
                    IsPersistent = true,
                    ExpiresUtc = DateTimeOffset.UtcNow.AddDays(1)
                });

            navigationManager.NavigateTo("/TestResultHauHNs/TestResultHauHnList", forceLoad: true);
        }
        else
        {
            navigationManager.NavigateTo("/Account/Login");
        }
    }
}