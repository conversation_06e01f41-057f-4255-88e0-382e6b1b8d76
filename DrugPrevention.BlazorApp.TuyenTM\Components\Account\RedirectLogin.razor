﻿@page "/Account/RedirectLogin/{username}"
@using DrugPrevention.Repositories.TuyenTM.Models
@using DrugPrevention.Services.TuyenTM
@using System.Security.Claims
@using Microsoft.AspNetCore.Authentication.Cookies
@using Microsoft.AspNetCore.Authentication
@inject IHttpContextAccessor HttpContextAccessor
@inject IServiceProviders _serviceProviders
@inject NavigationManager navigationManager


@code {
    [Parameter]
    public string? Username { get; set; }

    protected override async Task OnInitializedAsync()
    {
        if (string.IsNullOrWhiteSpace(Username))
        {
            navigationManager.NavigateTo("/Account/Login");
            return;
        }

        var user = await _serviceProviders.System_UserAccountService.GetUserAccountByUserNameAsync(Username);
        if (user != null && user.UserAccountID > 0 && user.IsActive)
        {
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.Name, user.UserName),
                new Claim(ClaimTypes.Role, user.RoleId.ToString()),
                new Claim("FullName", user.FullName ?? user.UserName),
                new Claim("Email", user.Email ?? ""),
                new Claim("UserAccountID", user.UserAccountID.ToString())
            };

            var identity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);
            var principal = new ClaimsPrincipal(identity);

            await HttpContextAccessor.HttpContext.SignInAsync(
                CookieAuthenticationDefaults.AuthenticationScheme,
                principal,
                new AuthenticationProperties
                {
                    IsPersistent = true,
                    ExpiresUtc = DateTimeOffset.UtcNow.AddDays(1)
                });

            // Navigate to the main page
            navigationManager.NavigateTo("/OrganizationProgramTuyenTMs/OrganizationProgramsTuyenTMList", forceLoad: true);
        }
        else
        {
            navigationManager.NavigateTo("/Account/Login");
        }
    }
}