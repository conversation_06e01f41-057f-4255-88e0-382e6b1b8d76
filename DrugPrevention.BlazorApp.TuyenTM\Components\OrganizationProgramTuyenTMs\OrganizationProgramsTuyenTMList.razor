@page "/OrganizationProgramTuyenTMs/OrganizationProgramsTuyenTMList"
@using DrugPrevention.Repositories.TuyenTM.Models
@attribute [StreamRendering]

<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h3 class="text-primary mb-0">
            <i class="bi bi-diagram-3-fill me-2"></i> Organization Programs Management
        </h3>
        <a href="/OrganizationProgramTuyenTMs/OrganizationProgramTuyenTMForm" class="btn btn-success">
            <i class="bi bi-plus-circle me-2"></i>Create New
        </a>
    </div>

    <div class="input-group mb-3">
        <span class="input-group-text"><i class="bi bi-search"></i></span>
        <input class="form-control" @bind="searchTerm" @bind:event="oninput" @oninput="OnSearchChanged" placeholder="Search by organization or program name..." />
    </div>

    @if (organizationProgramsTuyenTMList == null)
    {
        <div class="d-flex align-items-center justify-content-center">
            <!-- From Uiverse.io by Nawsome -->
            <div aria-label="Orange and tan hamster running in a metal wheel" role="img" class="wheel-and-hamster">
                <div class="wheel"></div>
                <div class="hamster">
                    <div class="hamster__body">
                        <div class="hamster__head">
                            <div class="hamster__ear"></div>
                            <div class="hamster__eye"></div>
                            <div class="hamster__nose"></div>
                        </div>
                        <div class="hamster__limb hamster__limb--fr"></div>
                        <div class="hamster__limb hamster__limb--fl"></div>
                        <div class="hamster__limb hamster__limb--br"></div>
                        <div class="hamster__limb hamster__limb--bl"></div>
                        <div class="hamster__tail"></div>
                    </div>
                </div>
                <div class="spoke"></div>
            </div>
        </div>
    }
    else if (filteredList.Count == 0)
    {
        <div class="alert alert-info">No matching data found.</div>
    }
    else
    {
        <div class="table-responsive">
            <table class="table table-bordered table-hover align-middle text-center">
                <thead class="table-primary">
                    <tr>
                        <th>ID</th>
                        <th>Organization</th>
                        <th>Program</th>
                        <th>Joined Date</th>
                        <th>Last Updated</th>
                        <th>Contribution</th>
                        <th>Sponsor?</th>
                        <th>Organizer?</th>
                        <th>Role</th>
                        <th>Funding</th>
                        <th>Feedback</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in paginatedList)
                    {
                        <tr>
                            <td>@item.OrganizationProgramTuyenTMID</td>
                            <td>@item.Organization?.OrganizationName</td>
                            <td>@item.ProgramToanNS?.ProgramName</td>
                            <td>@item.JoinedDate.ToString("dd-MM-yyyy")</td>
                            <td>@item.LastUpdated?.ToString("dd-MM-yyyy")</td>
                            <td class="text-start">@item.ContributionDescription</td>
                            <td>
                                @if (item.IsSponsor == true)
                                {
                                    <span class="badge bg-success">Yes</span>
                                }
                                else
                                {
                                    <span class="badge bg-secondary">No</span>
                                }
                            </td>
                            <td>
                                @if (item.IsOrganizer == true)
                                {
                                    <span class="badge bg-success">Yes</span>
                                }
                                else
                                {
                                    <span class="badge bg-secondary">No</span>
                                }
                            </td>
                            <td>@item.ProgramRole</td>
                            <td>@(item.FundingAmount.HasValue ? $"{item.FundingAmount:C}" : "N/A")</td>
                            <td class="text-start">@item.Feedback</td>
                            <td>
                                <a class="btn btn-sm btn-primary me-1" href="@($"/OrganizationProgramTuyenTMs/OrganizationProgramTuyenTMForm/{item.OrganizationProgramTuyenTMID}")">
                                    <i class="bi bi-pencil-fill"></i>
                                </a>
                                <a class="btn btn-sm btn-info me-1 text-white" href="@($"/OrganizationProgramTuyenTMs/Details/{item.OrganizationProgramTuyenTMID}")">
                                    <i class="bi bi-info-circle-fill"></i>
                                </a>
                                <a class="btn btn-sm btn-danger" href="@($"/OrganizationProgramTuyenTMs/Delete/{item.OrganizationProgramTuyenTMID}")"
                                   onclick="return confirm('Are you sure you want to delete this record?');">
                                    <i class="bi bi-trash-fill"></i>
                                </a>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        @if (totalPages > 1)
        {
            <div class="d-flex justify-content-between align-items-center mt-4">
                <div class="text-muted">
                    Showing @((currentPage - 1) * pageSize + 1) to @(Math.Min(currentPage * pageSize, totalItems)) of @totalItems entries
                </div>

                <nav aria-label="Page navigation">
                    <ul class="pagination pagination-sm mb-0">
                        <!-- Previous Button -->
                        <li class="page-item @(currentPage == 1 ? "disabled" : "")">
                            <button class="page-link" @onclick="() => GoToPage(currentPage - 1)" disabled="@(currentPage == 1)">
                                <i class="bi bi-chevron-left"></i> Previous
                            </button>
                        </li>

                        <!-- Page Numbers -->
                        @for (int i = Math.Max(1, currentPage - 2); i <= Math.Min(totalPages, currentPage + 2); i++)
                        {
                            int pageNumber = i;
                            <li class="page-item @(currentPage == pageNumber ? "active" : "")">
                                <button class="page-link" @onclick="() => GoToPage(pageNumber)">
                                    @pageNumber
                                </button>
                            </li>
                        }

                        <!-- Next Button -->
                        <li class="page-item @(currentPage == totalPages ? "disabled" : "")">
                            <button class="page-link" @onclick="() => GoToPage(currentPage + 1)" disabled="@(currentPage == totalPages)">
                                Next <i class="bi bi-chevron-right"></i>
                            </button>
                        </li>
                    </ul>
                </nav>
            </div>
        }
    }
</div>

@code {
    private List<OrganizationProgramsTuyenTM>? organizationProgramsTuyenTMList;
    private string searchTerm = string.Empty;

    // Pagination properties
    private int currentPage = 1;
    private int pageSize = 5;

    private List<OrganizationProgramsTuyenTM> filteredList =>
        organizationProgramsTuyenTMList == null ? new List<OrganizationProgramsTuyenTM>() :
        string.IsNullOrWhiteSpace(searchTerm)
        ? organizationProgramsTuyenTMList
        : organizationProgramsTuyenTMList.Where(x =>
               x.Organization?.OrganizationName?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) == true
            || x.ProgramToanNS?.ProgramName?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) == true
        ).ToList();

    private List<OrganizationProgramsTuyenTM> paginatedList =>
        filteredList.Skip((currentPage - 1) * pageSize).Take(pageSize).ToList();

    private int totalPages => (int)Math.Ceiling((double)filteredList.Count / pageSize);
    private int totalItems => filteredList.Count;

    protected override async Task OnInitializedAsync()
    {
        await Task.Delay(1000);
        organizationProgramsTuyenTMList = await _serviceProviders.OrganizationProgramsTuyenTMService.GetAllAsync();
    }

    private void GoToPage(int page)
    {
        if (page >= 1 && page <= totalPages)
        {
            currentPage = page;
            StateHasChanged();
        }
    }

    private void OnSearchChanged()
    {
        currentPage = 1; // Reset to first page when searching
        StateHasChanged();
    }
}
