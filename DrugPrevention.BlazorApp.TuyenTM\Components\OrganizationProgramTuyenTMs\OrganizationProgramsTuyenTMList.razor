﻿@page "/OrganizationProgramTuyenTMs/OrganizationProgramsTuyenTMList"
@using DrugPrevention.Repositories.TuyenTM.Models
@attribute [StreamRendering]

<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h3 class="text-primary mb-0">
            <i class="bi bi-diagram-3-fill me-2"></i> Organization Programs Management
        </h3>
        <a href="/OrganizationProgramTuyenTMs/OrganizationProgramTuyenTMForm" class="btn btn-success">
            <i class="bi bi-plus-circle me-2"></i>Create New
        </a>
    </div>

    <div class="input-group mb-3">
        <span class="input-group-text"><i class="bi bi-search"></i></span>
        <input class="form-control" @bind="searchTerm" @bind:event="oninput" placeholder="Search by organization or program name..." />
    </div>

    @if (organizationProgramsTuyenTMList == null)
    {
        <div class="d-flex align-items-center justify-content-center">
            <!-- From Uiverse.io by Nawsome -->
            <div aria-label="Orange and tan hamster running in a metal wheel" role="img" class="wheel-and-hamster">
                <div class="wheel"></div>
                <div class="hamster">
                    <div class="hamster__body">
                        <div class="hamster__head">
                            <div class="hamster__ear"></div>
                            <div class="hamster__eye"></div>
                            <div class="hamster__nose"></div>
                        </div>
                        <div class="hamster__limb hamster__limb--fr"></div>
                        <div class="hamster__limb hamster__limb--fl"></div>
                        <div class="hamster__limb hamster__limb--br"></div>
                        <div class="hamster__limb hamster__limb--bl"></div>
                        <div class="hamster__tail"></div>
                    </div>
                </div>
                <div class="spoke"></div>
            </div>
        </div>
    }
    else if (filteredList.Count == 0)
    {
        <div class="alert alert-info">No matching data found.</div>
    }
    else
    {
        <div class="table-responsive">
            <table class="table table-bordered table-hover align-middle text-center">
                <thead class="table-primary">
                    <tr>
                        <th>ID</th>
                        <th>Organization</th>
                        <th>Program</th>
                        <th>Joined Date</th>
                        <th>Last Updated</th>
                        <th>Contribution</th>
                        <th>Sponsor?</th>
                        <th>Organizer?</th>
                        <th>Role</th>
                        <th>Funding</th>
                        <th>Feedback</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in filteredList)
                    {
                        <tr>
                            <td>@item.OrganizationProgramTuyenTMID</td>
                            <td>@item.Organization?.OrganizationName</td>
                            <td>@item.ProgramToanNS?.ProgramName</td>
                            <td>@item.JoinedDate.ToString("dd-MM-yyyy")</td>
                            <td>@item.LastUpdated?.ToString("dd-MM-yyyy")</td>
                            <td class="text-start">@item.ContributionDescription</td>
                            <td>
                                @if (item.IsSponsor == true)
                                {
                                    <span class="badge bg-success">Yes</span>
                                }
                                else
                                {
                                    <span class="badge bg-secondary">No</span>
                                }
                            </td>
                            <td>
                                @if (item.IsOrganizer == true)
                                {
                                    <span class="badge bg-success">Yes</span>
                                }
                                else
                                {
                                    <span class="badge bg-secondary">No</span>
                                }
                            </td>
                            <td>@item.ProgramRole</td>
                            <td>@(item.FundingAmount.HasValue ? $"{item.FundingAmount:C}" : "N/A")</td>
                            <td class="text-start">@item.Feedback</td>
                            <td>
                                <a class="btn btn-sm btn-primary me-1" href="@($"/OrganizationProgramTuyenTMs/OrganizationProgramTuyenTMForm/{item.OrganizationProgramTuyenTMID}")">
                                    <i class="bi bi-pencil-fill"></i>
                                </a>
                                <a class="btn btn-sm btn-info me-1 text-white" href="@($"/OrganizationProgramTuyenTMs/Details/{item.OrganizationProgramTuyenTMID}")">
                                    <i class="bi bi-info-circle-fill"></i>
                                </a>
                                <a class="btn btn-sm btn-danger" href="@($"/OrganizationProgramTuyenTMs/Delete/{item.OrganizationProgramTuyenTMID}")"
                                   onclick="return confirm('Are you sure you want to delete this record?');">
                                    <i class="bi bi-trash-fill"></i>
                                </a>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    }
</div>

@code {
    private List<OrganizationProgramsTuyenTM>? organizationProgramsTuyenTMList;
    private string searchTerm = string.Empty;

    private List<OrganizationProgramsTuyenTM> filteredList =>
        organizationProgramsTuyenTMList == null ? new List<OrganizationProgramsTuyenTM>() :
        string.IsNullOrWhiteSpace(searchTerm)
        ? organizationProgramsTuyenTMList
        : organizationProgramsTuyenTMList.Where(x =>
               x.Organization?.OrganizationName?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) == true
            || x.ProgramToanNS?.ProgramName?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) == true
        ).ToList();

    protected override async Task OnInitializedAsync()
    {
        await Task.Delay(1000);
        organizationProgramsTuyenTMList = await _serviceProviders.OrganizationProgramsTuyenTMService.GetAllAsync();
    }
}
