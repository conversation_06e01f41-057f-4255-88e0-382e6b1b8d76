@page "/OrganizationProgramTuyenTMs/Delete/{id:int}"
@using DrugPrevention.Repositories.TuyenTM.Models
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Web
@attribute [StreamRendering]
@rendermode InteractiveServer

<PageTitle>Delete Organization Program</PageTitle>

<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8 col-xl-6">

            @if (organizationProgram == null)
            {
                <div class="d-flex justify-content-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            }
            else
            {
                <!-- Header Card -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-danger text-white">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-trash me-2"></i>
                            <h4 class="mb-0">Delete Organization Program</h4>
                        </div>
                    </div>
                </div>

                <!-- Confirmation Card -->
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="alert alert-warning d-flex align-items-center" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <div>
                                <strong>Warning!</strong> Are you sure you want to delete this organization program? This action cannot be undone.
                            </div>
                        </div>

                        <!-- Program Details -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-info-circle me-2"></i>Program Information
                                </h5>
                            </div>
                        </div>

                        <dl class="row">
                            <dt class="col-sm-4">Organization:</dt>
                            <dd class="col-sm-8">@(organizationProgram.Organization?.OrganizationName ?? "N/A")</dd>

                            <dt class="col-sm-4">Program:</dt>
                            <dd class="col-sm-8">@(organizationProgram.ProgramToanNS?.ProgramName ?? "N/A")</dd>

                            <dt class="col-sm-4">Joined Date:</dt>
                            <dd class="col-sm-8">@organizationProgram.JoinedDate.ToString("MMM dd, yyyy")</dd>

                            <dt class="col-sm-4">Program Role:</dt>
                            <dd class="col-sm-8">@(organizationProgram.ProgramRole ?? "N/A")</dd>

                            <dt class="col-sm-4">Funding Amount:</dt>
                            <dd class="col-sm-8">@(organizationProgram.FundingAmount.HasValue ? $"{organizationProgram.FundingAmount:C}" : "N/A")</dd>

                            <dt class="col-sm-4">Is Sponsor:</dt>
                            <dd class="col-sm-8">
                                @if (organizationProgram.IsSponsor == true)
                                {
                                    <span class="badge bg-success">Yes</span>
                                }
                                else
                                {
                                    <span class="badge bg-secondary">No</span>
                                }
                            </dd>

                            <dt class="col-sm-4">Is Organizer:</dt>
                            <dd class="col-sm-8">
                                @if (organizationProgram.IsOrganizer == true)
                                {
                                    <span class="badge bg-primary">Yes</span>
                                }
                                else
                                {
                                    <span class="badge bg-secondary">No</span>
                                }
                            </dd>

                            @if (!string.IsNullOrEmpty(organizationProgram.ContributionDescription))
                            {
                                <dt class="col-sm-4">Contribution:</dt>
                                <dd class="col-sm-8">@organizationProgram.ContributionDescription</dd>
                            }

                            @if (!string.IsNullOrEmpty(organizationProgram.Feedback))
                            {
                                <dt class="col-sm-4">Feedback:</dt>
                                <dd class="col-sm-8">@organizationProgram.Feedback</dd>
                            }
                        </dl>

                        <!-- Action Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex gap-2 justify-content-end">
                                    <button type="button"
                                            class="btn btn-outline-secondary"
                                            @onclick="Cancel"
                                            disabled="@isDeleting">
                                        <i class="fas fa-times me-2"></i>Cancel
                                    </button>

                                    <button type="button"
                                            class="btn btn-danger"
                                            @onclick="ConfirmDelete"
                                            disabled="@isDeleting">
                                        @if (isDeleting)
                                        {
                                            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                            <span>Deleting...</span>
                                        }
                                        else
                                        {
                                            <i class="fas fa-trash me-2"></i>
                                            <span>Delete Program</span>
                                        }
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Error Message -->
                        @if (!string.IsNullOrEmpty(errorMessage))
                        {
                            <div class="row mt-3">
                                <div class="col-12">
                                    <div class="alert alert-danger d-flex align-items-center" role="alert">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        <div>@errorMessage</div>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            }
        </div>
    </div>
</div>

@code {
    [Parameter] public int id { get; set; }
    private OrganizationProgramsTuyenTM? organizationProgram;
    private bool isDeleting = false;
    private string? errorMessage;

    protected override async Task OnInitializedAsync()
    {
        organizationProgram = await _serviceProviders.OrganizationProgramsTuyenTMService.GetByIdAsync(id);
        
        if (organizationProgram == null)
        {
            navigationManager.NavigateTo("/OrganizationProgramTuyenTMs/OrganizationProgramTuyenTMList");
        }
    }

    private async Task ConfirmDelete()
    {
        if (isDeleting) return;
        isDeleting = true;
        errorMessage = null;

        try
        {
            bool result = await _serviceProviders.OrganizationProgramsTuyenTMService.DeleteAsync(id);

            if (result)
            {
                navigationManager.NavigateTo("/OrganizationProgramTuyenTMs/OrganizationProgramTuyenTMList");
            }
            else
            {
                errorMessage = "Failed to delete the program.";
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"Error: {ex.Message}";
        }
        finally
        {
            isDeleting = false;
        }
    }

    private void Cancel()
    {
        navigationManager.NavigateTo("/OrganizationProgramTuyenTMs/OrganizationProgramTuyenTMList");
    }
}
