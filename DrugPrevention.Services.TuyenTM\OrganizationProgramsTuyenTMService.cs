using DrugPrevention.Repositories.TuyenTM;
using DrugPrevention.Repositories.TuyenTM.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DrugPrevention.Services.TuyenTM
{
    public class OrganizationProgramsTuyenTMService : IOrganizationProgramsTuyenTMService
    {
        //private readonly OrganizationProgramsTuyenTMRepository _repository;
        private readonly UnitOfWork _unitOfWork;
        public OrganizationProgramsTuyenTMService() => _unitOfWork = new UnitOfWork();

        public async Task<List<OrganizationProgramsTuyenTM>> GetAllAsync()
        {
            return await _unitOfWork.OrganizationProgramsTuyenTMRepository.GetAllAsync();
        }

        public async Task<OrganizationProgramsTuyenTM> GetByIdAsync(int id)
        {
            return await _unitOfWork.OrganizationProgramsTuyenTMRepository.GetByIdAsync(id);
        }

        public async Task<List<OrganizationProgramsTuyenTM>> SearchAsync(int id, string name, string type)
        {
            return await _unitOfWork.OrganizationProgramsTuyenTMRepository.SearchAsync(id, name, type);
        }

        public async Task<int> AddAsync(OrganizationProgramsTuyenTM program)
        {
            return await _unitOfWork.OrganizationProgramsTuyenTMRepository.CreateAsync(program);
        }

        public async Task<int> UpdateAsync(OrganizationProgramsTuyenTM program)
        {
            try
            {
                // Get the existing entity from database
                var existing = await _unitOfWork.OrganizationProgramsTuyenTMRepository.GetByIdAsync(program.OrganizationProgramTuyenTMID);
                if (existing == null)
                {
                    Console.WriteLine($"UpdateAsync - Entity with ID {program.OrganizationProgramTuyenTMID} not found");
                    return 0;
                }

                // Update only the fields that can be changed
                existing.OrganizationID = program.OrganizationID;
                existing.ProgramToanNSID = program.ProgramToanNSID;
                existing.ContributionDescription = program.ContributionDescription;
                existing.IsSponsor = program.IsSponsor;
                existing.IsOrganizer = program.IsOrganizer;
                existing.LastUpdated = DateTime.Now;

                // Log the values being updated for debugging
                Console.WriteLine($"UpdateAsync - ID: {existing.OrganizationProgramTuyenTMID}, OrgID: {existing.OrganizationID}, ProgramID: {existing.ProgramToanNSID}");

                return await _unitOfWork.OrganizationProgramsTuyenTMRepository.UpdateAsync(existing);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"UpdateAsync - Error: {ex.Message}");
                throw;
            }
        }

        public async Task<bool> DeleteAsync(int id)
        {
            var program = await _unitOfWork.OrganizationProgramsTuyenTMRepository.GetByIdAsync(id);
            if (program != null)
            {
                return await _unitOfWork.OrganizationProgramsTuyenTMRepository.RemoveAsync(program);
            }
            return false;
        }
    }
}
