﻿@page "/Account/Login"
@using DNATesting.Repositories.HauHN.Models
@using System.Security.Claims
@using Microsoft.AspNetCore.Authentication
@using Microsoft.AspNetCore.Authentication.Cookies

<h3 class="text-danger flex-lg-wrap text-center ">Blood Donation System</h3>

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4 shadow p-4 rounded border">
            <h4 class="text-center mb-2">Sign In</h4>

            <EditForm Model="@loginModel" OnValidSubmit="HandleLogin">
                <DataAnnotationsValidator />
                <ValidationSummary />

                <div class="mb-3">
                    <label class="form-label">Username</label>
                    <InputText @bind-Value="loginModel.UserName" class="form-control" />
                </div>

                <div class="mb-3">
                    <label class="form-label">Password</label>
                    <InputText @bind-Value="loginModel.Password" class="form-control" type="password" />
                </div>

                <button type="submit" class="btn btn-outline-dark w-100 mt-2">Log In</button>

                @if (!string.IsNullOrEmpty(errorMessage))
                {
                    <div class="text-danger mt-2">@errorMessage</div>
                }
            </EditForm>
        </div>
    </div>
</div>


@code {
    private SystemUserAccount? systemUserAccount;
    private LoginModel loginModel { get; set; } = new LoginModel();
    private string? errorMessage;

    // userAccount se =  await _serviceProviders.SystemUserAccountService.CheckLogin(UserName, Password);)

    private async Task HandleLogin()
    {
        var userAccount = await ServiceProviders.systemUserAccountService.GetUserAccountAsync(loginModel.UserName, loginModel.Password);

        if (userAccount != null)
        {
            await runtimeJS.InvokeVoidAsync("eval", $"window.location.href = '/Account/RedirectLogin/{loginModel.UserName}'");
        }
        else
        {
            errorMessage = "Invalid password or username, try again!";
        }
    }

    private class LoginModel
    {
        public string UserName { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
    }
}