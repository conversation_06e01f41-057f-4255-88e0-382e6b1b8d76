﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace DrugPrevention.Repositories.TuyenTM.Models;

public partial class OrganizationProgramsTuyenTM
{
    public int OrganizationProgramTuyenTMID { get; set; }

    public int OrganizationID { get; set; }

    public int ProgramToanNSID { get; set; }

    public DateTime JoinedDate { get; set; }

    [Required(ErrorMessage = "Contribution description is required.")]
    [StringLength(500, ErrorMessage = "Contribution description cannot exceed 500 characters.")]
    public string ContributionDescription { get; set; }

    public bool? IsSponsor { get; set; }

    public bool? IsOrganizer { get; set; }

    public string ProgramRole { get; set; }

    public decimal? FundingAmount { get; set; }

    public string Feedback { get; set; }

    public DateTime? LastUpdated { get; set; }

    public virtual OrganizationsTuyenTM Organization { get; set; }

    public virtual CommunityProgramsToanN ProgramToanNS { get; set; }
}