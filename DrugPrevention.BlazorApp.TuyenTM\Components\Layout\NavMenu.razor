﻿@using Microsoft.AspNetCore.Components.Authorization
@inject AuthenticationStateProvider AuthenticationStateProvider

<div class="top-row ps-3 navbar navbar-dark">
    <div class="container-fluid d-flex justify-content-between align-items-center">
        <span class="navbar-brand">Drug Prevention System</span>
        <AuthorizeView>
            <Authorized>
                <div class="d-flex align-items-center">
                    <span class="text-light me-3">Welcome, @context.User.Identity?.Name</span>
                    <a href="/Account/Logout" class="btn btn-outline-light btn-sm">
                        <span class="bi bi-box-arrow-right" aria-hidden="true"></span> Logout
                    </a>
                </div>
            </Authorized>
            <NotAuthorized>
                <a href="/Account/Login" class="btn btn-outline-light btn-sm">
                    <span class="bi bi-box-arrow-in-right" aria-hidden="true"></span> Login
                </a>
            </NotAuthorized>
        </AuthorizeView>
    </div>
</div>

<input type="checkbox" title="Navigation menu" class="navbar-toggler" />

<div class="nav-scrollable" onclick="document.querySelector('.navbar-toggler').click()">
    <nav class="flex-column">
        <AuthorizeView>
            <Authorized>
                <div class="nav-item px-3">
                    <NavLink class="nav-link" href="" Match="NavLinkMatch.All">
                        <span class="bi bi-house-door-fill-nav-menu" aria-hidden="true"></span> Home
                    </NavLink>
                </div>

                <div class="nav-item px-3">
                    <NavLink class="nav-link" href="counter">
                        <span class="bi bi-plus-square-fill-nav-menu" aria-hidden="true"></span> Counter
                    </NavLink>
                </div>

                <div class="nav-item px-3">
                    <NavLink class="nav-link" href="/OrganizationProgramTuyenTMs/OrganizationProgramsTuyenTMList" Match="NavLinkMatch.All">
                        <span class="bi bi-list-nested-nav-menu" aria-hidden="true"></span> OrgProgramManagement
                    </NavLink>
                </div>
            </Authorized>
            <NotAuthorized>
                <div class="nav-item px-3">
                    <NavLink class="nav-link" href="/Account/Login">
                        <span class="bi bi-box-arrow-in-right" aria-hidden="true"></span> Please Login
                    </NavLink>
                </div>
            </NotAuthorized>
        </AuthorizeView>
    </nav>
</div>

